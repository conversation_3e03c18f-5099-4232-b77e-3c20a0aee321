# Python Chainlit Chat Application

A simple yet powerful chat application built with [Chainlit](https://chainlit.io/), demonstrating various event handlers and interactive chat features.

## 🚀 Features

- **Real-time Chat Interface**: Interactive web-based chat UI
- **Event Handling**: Comprehensive event system including:
  - Chat session start/end
  - Message handling
  - Task stopping
  - Chat resumption
- **Hot Reload**: Development mode with automatic reloading
- **Modern Python**: Built with Python 3.11+ and UV package manager

## 📋 Prerequisites

- Python 3.11 or higher
- [UV](https://docs.astral.sh/uv/) package manager (recommended) or pip

## 🛠️ Installation

### Using UV (Recommended)

1. Clone the repository:
```bash
git clone https://github.com/asadullah48/python-chainlit.git
cd python-chainlit
```

2. Install dependencies:
```bash
uv sync
```

### Using pip

1. Clone the repository:
```bash
git clone https://github.com/asadullah48/python-chainlit.git
cd python-chainlit
```

2. Create a virtual environment:
```bash
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install chainlit>=2.6.0
```

## 🚀 Usage

### Development Mode (with hot reload)

```bash
chainlit run app.py -w
```

### Production Mode

```bash
chainlit run app.py
```

The application will be available at `http://localhost:8000`

## 📁 Project Structure

```
python-chainlit/
├── app.py              # Main application file
├── chainlit.md         # Chainlit configuration
├── pyproject.toml      # Project dependencies
├── README.md           # This file
└── .venv/              # Virtual environment (if using pip)
```

## 🔧 Code Overview

The application demonstrates several Chainlit event handlers:

- **`@cl.on_chat_start`**: Triggered when a new chat session begins
- **`@cl.on_message`**: Handles incoming user messages
- **`@cl.on_stop`**: Called when user stops a running task
- **`@cl.on_chat_end`**: Triggered when user disconnects
- **`@cl.on_chat_resume`**: Handles chat session resumption

## 🎯 Example Usage

1. Start the application
2. Open your browser to `http://localhost:8000`
3. Type a message and see the echo response
4. Check the terminal for event logs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🔗 Links

- [Chainlit Documentation](https://docs.chainlit.io/)
- [Chainlit GitHub](https://github.com/Chainlit/chainlit)
- [UV Documentation](https://docs.astral.sh/uv/)

## 👤 Author

**asadullah48**
- GitHub: [@asadullah48](https://github.com/asadullah48)

---

⭐ Star this repository if you found it helpful!