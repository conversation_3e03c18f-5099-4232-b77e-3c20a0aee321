# AI Configuration Template
# Copy this file to .env and add your actual API keys

# ===========================================
# GEMINI (Google AI) Configuration
# ===========================================
# Get your API key from: https://aistudio.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Available Gemini models:
# - gemini-2.0-flash-exp (latest experimental)
# - gemini-1.5-pro
# - gemini-1.5-flash
MODEL_NAME=gemini-2.0-flash-exp

# ===========================================
# OPENAI Configuration (Alternative)
# ===========================================
# Uncomment these lines if you want to use OpenAI instead
# Get your API key from: https://platform.openai.com/api-keys
# OPENAI_API_KEY=your_openai_api_key_here
# MODEL_NAME=gpt-4

# ===========================================
# Optional Settings
# ===========================================
# Temperature controls randomness (0.0 = deterministic, 2.0 = very random)
TEMPERATURE=0.7

# Maximum tokens in response
MAX_TOKENS=1000

# ===========================================
# Instructions
# ===========================================
# 1. Copy this file: cp .env.example .env
# 2. Edit .env and replace "your_api_key_here" with your actual API key
# 3. Choose either Gemini or OpenAI by setting the appropriate API key
# 4. Run: chainlit run stream.py -w
