import chainlit as cl

@cl.on_message
async def main(message: cl.Message):
    """Simple message handler"""
    print(f"📨 User sent: {message.content}")

    # Send enhanced response
    await cl.Message(
        content=f"**Received:** {message.content}\n\n✨ This is your enhanced response! The message had {len(message.content.split())} words."
    ).send()

@cl.step
async def my_step():
    current_step = cl.context.current_step

    # Override the input of the step
    current_step.input = "My custom input"

    # Override the output of the step
    current_step.output = "My custom output"