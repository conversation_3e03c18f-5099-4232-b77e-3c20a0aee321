import chainlit as cl
from datetime import datetime
import asyncio

# Store user sessions and profile data
user_sessions = {}
profile_analytics = {
    "coding_assistant": {"sessions": 0, "messages": 0},
    "creative_writer": {"sessions": 0, "messages": 0},
    "data_analyst": {"sessions": 0, "messages": 0},
    "learning_tutor": {"sessions": 0, "messages": 0},
    "productivity_coach": {"sessions": 0, "messages": 0}
}

@cl.set_chat_profiles
async def chat_profile():
    """Define multiple specialized chat profiles for different use cases"""
    return [
        cl.ChatProfile(
            name="coding_assistant",
            icon="🐍",
            markdown_description="""
# 🐍 **Coding Assistant**

Your personal programming companion powered by advanced AI.

**Specializations:**
- **Python Development** - Scripts, automation, web apps
- **JavaScript/TypeScript** - Frontend and backend solutions
- **Data Science** - Pandas, NumPy, machine learning
- **DevOps** - Docker, CI/CD, cloud deployment
- **Code Review** - Best practices and optimization

**Features:**
- ✅ Complete code examples with explanations
- ✅ Debugging and error resolution
- ✅ Architecture recommendations
- ✅ Performance optimization tips
- ✅ Testing strategies and examples

*Perfect for developers of all skill levels!*
            """,
            starters=[
                cl.Starter(
                    label="Python Web Scraper",
                    message="Create a Python script to scrape product prices from an e-commerce website and save them to a CSV file. Include error handling and rate limiting.",
                    icon="🕷️",
                ),
                cl.Starter(
                    label="REST API with FastAPI",
                    message="Build a complete REST API using FastAPI with user authentication, database integration, and proper error handling. Include example endpoints.",
                    icon="🚀",
                ),
                cl.Starter(
                    label="Data Analysis Pipeline",
                    message="Create a data analysis pipeline that reads CSV files, cleans the data, performs statistical analysis, and generates visualizations using pandas and matplotlib.",
                    icon="📊",
                ),
                cl.Starter(
                    label="Debug My Code",
                    message="I'm getting an error in my Python code. Can you help me debug it and explain what's going wrong? I'll share the code and error message.",
                    icon="🐛",
                ),
            ],
        ),

        cl.ChatProfile(
            name="creative_writer",
            icon="✍️",
            markdown_description="""
# ✍️ **Creative Writer**

Your imaginative writing partner for all creative endeavors.

**Specializations:**
- **Fiction Writing** - Stories, novels, character development
- **Content Creation** - Blogs, articles, social media
- **Business Writing** - Emails, proposals, presentations
- **Creative Projects** - Poetry, scripts, creative non-fiction
- **Editing & Proofreading** - Grammar, style, flow improvement

**Features:**
- ✅ Genre-specific writing assistance
- ✅ Character and plot development
- ✅ Tone and style adaptation
- ✅ Research and fact-checking
- ✅ Multiple draft iterations

*Unleash your creativity with AI-powered writing support!*
            """,
            starters=[
                cl.Starter(
                    label="Short Story Generator",
                    message="Help me write a compelling short story. Start by asking about my preferred genre, setting, and main character, then guide me through the plot development.",
                    icon="📚",
                ),
                cl.Starter(
                    label="Business Email Templates",
                    message="Create professional email templates for common business scenarios: follow-ups, meeting requests, project updates, and client communications.",
                    icon="📧",
                ),
                cl.Starter(
                    label="Social Media Content",
                    message="Generate engaging social media content for a week including posts, captions, and hashtags for LinkedIn, Twitter, and Instagram. Focus on tech industry topics.",
                    icon="📱",
                ),
                cl.Starter(
                    label="Blog Article Outline",
                    message="Help me create a detailed outline for a blog article about emerging technology trends. Include key points, research suggestions, and SEO considerations.",
                    icon="📝",
                ),
            ],
        ),

        cl.ChatProfile(
            name="data_analyst",
            icon="📊",
            markdown_description="""
# 📊 **Data Analyst**

Your analytical powerhouse for data-driven insights.

**Specializations:**
- **Data Visualization** - Charts, dashboards, interactive plots
- **Statistical Analysis** - Hypothesis testing, regression, correlation
- **Business Intelligence** - KPIs, metrics, reporting
- **Machine Learning** - Predictive models, classification, clustering
- **Database Queries** - SQL optimization, data extraction

**Features:**
- ✅ Python/R code for analysis
- ✅ Statistical interpretation
- ✅ Visualization recommendations
- ✅ Business insights generation
- ✅ Data cleaning strategies

*Transform raw data into actionable business intelligence!*
            """,
            starters=[
                cl.Starter(
                    label="Sales Data Analysis",
                    message="Analyze sales data to identify trends, seasonal patterns, and top-performing products. Create visualizations and provide business recommendations.",
                    icon="💰",
                ),
                cl.Starter(
                    label="Customer Segmentation",
                    message="Perform customer segmentation analysis using clustering algorithms. Help me understand different customer groups and their characteristics.",
                    icon="👥",
                ),
                cl.Starter(
                    label="A/B Test Analysis",
                    message="Design and analyze an A/B test for a website feature. Include statistical significance testing and recommendations for implementation.",
                    icon="🧪",
                ),
                cl.Starter(
                    label="Dashboard Creation",
                    message="Create an interactive dashboard using Python (Plotly/Streamlit) to visualize key business metrics and KPIs with real-time data updates.",
                    icon="📈",
                ),
            ],
        ),

        cl.ChatProfile(
            name="learning_tutor",
            icon="🎓",
            markdown_description="""
# 🎓 **Learning Tutor**

Your personalized education companion for mastering new skills.

**Specializations:**
- **Programming Concepts** - Algorithms, data structures, design patterns
- **Mathematics** - Statistics, calculus, linear algebra
- **Science Topics** - Physics, chemistry, computer science
- **Technology Trends** - AI/ML, blockchain, cloud computing
- **Skill Development** - Learning strategies, practice exercises

**Features:**
- ✅ Step-by-step explanations
- ✅ Interactive examples and exercises
- ✅ Progress tracking suggestions
- ✅ Multiple learning approaches
- ✅ Real-world applications

*Learn faster with personalized, adaptive instruction!*
            """,
            starters=[
                cl.Starter(
                    label="Learn Machine Learning",
                    message="I want to learn machine learning from scratch. Create a structured learning path with key concepts, practical exercises, and project ideas.",
                    icon="🤖",
                ),
                cl.Starter(
                    label="Algorithm Explanation",
                    message="Explain sorting algorithms (bubble sort, merge sort, quick sort) with visual examples and help me understand when to use each one.",
                    icon="🔄",
                ),
                cl.Starter(
                    label="Statistics Fundamentals",
                    message="Teach me statistics fundamentals including probability, distributions, hypothesis testing, and confidence intervals with practical examples.",
                    icon="📐",
                ),
                cl.Starter(
                    label="System Design Basics",
                    message="Explain system design principles for building scalable applications. Cover load balancing, databases, caching, and microservices architecture.",
                    icon="🏗️",
                ),
            ],
        ),

        cl.ChatProfile(
            name="productivity_coach",
            icon="⚡",
            markdown_description="""
# ⚡ **Productivity Coach**

Your personal efficiency expert for optimizing life and work.

**Specializations:**
- **Time Management** - Scheduling, prioritization, focus techniques
- **Habit Formation** - Building routines, breaking bad habits
- **Goal Setting** - SMART goals, tracking, accountability
- **Workflow Optimization** - Automation, tools, processes
- **Work-Life Balance** - Stress management, boundaries

**Features:**
- ✅ Personalized productivity strategies
- ✅ Habit tracking systems
- ✅ Time audit and optimization
- ✅ Tool recommendations
- ✅ Progress monitoring

*Maximize your potential with proven productivity methods!*
            """,
            starters=[
                cl.Starter(
                    label="Morning Routine Design",
                    message="Help me design the perfect morning routine to boost productivity, energy, and focus throughout the day. Consider my lifestyle and goals.",
                    icon="🌅",
                ),
                cl.Starter(
                    label="Time Blocking System",
                    message="Create a time blocking system for my work schedule. Help me prioritize tasks, minimize distractions, and maximize deep work sessions.",
                    icon="⏰",
                ),
                cl.Starter(
                    label="Habit Tracker Setup",
                    message="Design a comprehensive habit tracking system to help me build positive habits and break negative ones. Include motivation strategies.",
                    icon="✅",
                ),
                cl.Starter(
                    label="Productivity Tools Audit",
                    message="Audit my current productivity tools and workflow. Recommend improvements, automations, and better tools to streamline my work.",
                    icon="🛠️",
                ),
            ],
        ),
    ]

@cl.on_chat_start
async def on_chat_start():
    """Initialize chat session with welcome message based on selected profile"""
    profile = cl.user_session.get("chat_profile")
    profile_name = profile.get("name") if profile else "default"

    # Update analytics
    if profile_name in profile_analytics:
        profile_analytics[profile_name]["sessions"] += 1

    # Store session info
    user_sessions[cl.context.session.id] = {
        "start_time": datetime.now(),
        "message_count": 0,
        "profile": profile_name
    }

    print(f"🚀 New chat session started with profile: {profile_name}")

    # Create profile-specific welcome message
    welcome_messages = {
        "coding_assistant": """# 🐍 Welcome to Coding Assistant!

I'm your AI programming partner, ready to help with:
- Writing and debugging code
- Explaining programming concepts
- Designing software architecture
- Optimizing performance
- Recommending best practices

**Try asking me to:**
- Create a specific script or function
- Debug an error in your code
- Explain a programming concept
- Recommend a design pattern
- Help with a coding challenge

What coding project can I help you with today?""",

        "creative_writer": """# ✍️ Welcome to Creative Writer!

I'm your AI writing companion, ready to help with:
- Fiction and storytelling
- Business and professional writing
- Content creation and marketing
- Editing and proofreading
- Creative ideation

**Try asking me to:**
- Help develop a story idea
- Draft an important email
- Create engaging social media content
- Provide feedback on your writing
- Generate creative prompts

What writing project shall we work on today?""",

        "data_analyst": """# 📊 Welcome to Data Analyst!

I'm your AI data partner, ready to help with:
- Data analysis and visualization
- Statistical methods and interpretation
- Business intelligence
- Machine learning concepts
- Database queries and optimization

**Try asking me to:**
- Analyze a dataset
- Explain statistical concepts
- Create visualization code
- Design a data pipeline
- Interpret analysis results

What data challenge can I help you solve today?""",

        "learning_tutor": """# 🎓 Welcome to Learning Tutor!

I'm your AI education companion, ready to help with:
- Learning new concepts step-by-step
- Explaining complex topics simply
- Creating study plans and resources
- Practice exercises and quizzes
- Tracking learning progress

**Try asking me to:**
- Explain a difficult concept
- Create a learning roadmap
- Generate practice problems
- Simplify complex information
- Recommend learning resources

What would you like to learn about today?""",

        "productivity_coach": """# ⚡ Welcome to Productivity Coach!

I'm your AI efficiency expert, ready to help with:
- Time management strategies
- Habit formation and tracking
- Goal setting and achievement
- Workflow optimization
- Work-life balance

**Try asking me to:**
- Design a productivity system
- Create a habit tracking plan
- Optimize your daily schedule
- Recommend focus techniques
- Develop a goal achievement strategy

How can I help boost your productivity today?"""
    }

    # Send welcome message based on profile
    welcome_content = welcome_messages.get(
        profile_name,
        "# 👋 Welcome to Chat Profiles!\n\nSelect a profile that matches your needs to get specialized assistance."
    )

    await cl.Message(content=welcome_content).send()

    # Show profile analytics for admin users
    if profile_name == "admin":
        analytics_content = "# 📈 Profile Analytics\n\n"
        for profile, stats in profile_analytics.items():
            analytics_content += f"**{profile}**: {stats['sessions']} sessions, {stats['messages']} messages\n"
        await cl.Message(content=analytics_content).send()

@cl.on_message
async def main(message: cl.Message):
    """Handle messages based on the selected profile"""
    session_id = cl.context.session.id
    session = user_sessions.get(session_id, {})
    profile_name = session.get("profile", "default")

    # Update message count
    session["message_count"] = session.get("message_count", 0) + 1
    user_sessions[session_id] = session

    # Update analytics
    if profile_name in profile_analytics:
        profile_analytics[profile_name]["messages"] += 1

    print(f"📨 User sent to {profile_name}: {message.content}")

    # Process message based on profile
    if profile_name == "coding_assistant":
        await handle_coding_message(message)
    elif profile_name == "creative_writer":
        await handle_creative_message(message)
    elif profile_name == "data_analyst":
        await handle_data_message(message)
    elif profile_name == "learning_tutor":
        await handle_learning_message(message)
    elif profile_name == "productivity_coach":
        await handle_productivity_message(message)
    else:
        # Default handler
        await handle_default_message(message)

async def handle_coding_message(message):
    """Handle coding assistant profile messages"""
    content = message.content.lower()

    # Detect programming language
    language = "python"  # Default
    if "javascript" in content or "js" in content:
        language = "javascript"
    elif "typescript" in content or "ts" in content:
        language = "typescript"
    elif "java" in content:
        language = "java"
    elif "c++" in content or "cpp" in content:
        language = "cpp"

    # Detect coding task
    task_type = "general"
    if "debug" in content or "error" in content or "fix" in content:
        task_type = "debugging"
    elif "explain" in content or "how" in content or "what" in content:
        task_type = "explanation"
    elif "optimize" in content or "performance" in content:
        task_type = "optimization"
    elif "create" in content or "build" in content or "implement" in content:
        task_type = "implementation"

    # Simulate thinking for complex coding tasks
    if task_type in ["implementation", "debugging", "optimization"]:
        thinking_msg = cl.Message(content=f"🧠 Analyzing your {task_type} request for {language}...")
        await thinking_msg.send()
        await asyncio.sleep(1.5)
        await thinking_msg.update(content=f"🧠 Analyzing your {task_type} request for {language}...\n\n✅ Determining requirements\n⏳ Designing solution")
        await asyncio.sleep(1.5)
        await thinking_msg.update(content=f"🧠 Analyzing your {task_type} request for {language}...\n\n✅ Determining requirements\n✅ Designing solution\n⏳ Implementing code")
        await asyncio.sleep(1.5)
        await thinking_msg.update(content=f"🧠 Analyzing your {task_type} request for {language}...\n\n✅ Determining requirements\n✅ Designing solution\n✅ Implementing code\n✅ Optimizing solution")

    # Generate response based on task type
    if task_type == "debugging":
        response = f"""# 🐛 Debugging Analysis

**Language Detected:** {language.capitalize()}
**Issue Type:** Error Analysis & Resolution

## Potential Issues:

1. **Syntax Errors**
   - Check for missing brackets, semicolons, or parentheses
   - Verify proper indentation (especially in Python)
   - Ensure string quotes are properly closed

2. **Logic Errors**
   - Verify loop conditions and exit criteria
   - Check variable scopes and lifetime
   - Validate conditional statements

3. **Runtime Errors**
   - Handle potential null/None values
   - Implement proper error handling with try/except
   - Check array/list bounds

## Example Fix:

```{language}
# Here's how you might fix the issue
# (Based on your description, this is a general example)

try:
    # Your problematic code would go here
    result = process_data(input_value)

    # Add validation
    if result is None:
        raise ValueError("Processing returned None")

    return result

except Exception as e:
    # Proper error handling
    logger.error(f"Error processing data: {e}")
    return default_value
```

Would you like me to analyze a specific piece of code? Please share the code and any error messages you're seeing."""

    elif task_type == "implementation":
        response = f"""# 🚀 Implementation Plan

**Language:** {language.capitalize()}
**Task Type:** New Feature Implementation

## Architecture Overview:

1. **Requirements Analysis**
   - Core functionality: {message.content}
   - Performance considerations
   - Error handling strategy

2. **Component Design**
   - Data structures
   - Function/class organization
   - API design

3. **Implementation Steps**

```{language}
# Implementation example
# Note: This is a simplified version based on your request

def main():
    # Initialize components
    config = load_configuration()

    # Set up resources
    resources = initialize_resources(config)

    # Main processing logic
    result = process_data(resources, input_data)

    # Output handling
    save_results(result, config.output_path)

    return result

def process_data(resources, data):
    # Data processing logic would go here
    # ...

    return processed_result
```

## Testing Strategy:
- Unit tests for core functions
- Integration tests for component interaction
- Performance benchmarks

Would you like me to expand on any part of this implementation or provide more detailed code?"""

    else:
        # General coding response
        response = f"""# 💻 Coding Assistant

**Language:** {language.capitalize()}
**Request:** {message.content}

I'd be happy to help with your coding request. To provide the most accurate assistance, could you please:

1. **Share any existing code** you're working with
2. **Specify requirements** in more detail
3. **Describe the context** of your project

This will help me generate code that's tailored to your specific needs rather than providing generic examples.

In the meantime, here are some resources for {language.capitalize()} development:

- **Documentation**: [Official {language.capitalize()} Docs](https://docs.python.org/3/) (for Python)
- **Style Guide**: Follow [PEP 8](https://peps.python.org/pep-0008/) for clean, readable code
- **Testing**: Consider using pytest for test-driven development
- **Virtual Environment**: Use venv or conda for dependency management

Ready to help when you provide more details!"""

    await cl.Message(content=response).send()

async def handle_creative_message(message):
    """Handle creative writer profile messages"""
    content = message.content.lower()

    # Detect writing type
    writing_type = "general"
    if "story" in content or "fiction" in content or "novel" in content:
        writing_type = "fiction"
    elif "email" in content or "business" in content or "professional" in content:
        writing_type = "business"
    elif "blog" in content or "article" in content or "post" in content:
        writing_type = "content"
    elif "social" in content or "tweet" in content or "instagram" in content:
        writing_type = "social"

    # Generate response based on writing type
    if writing_type == "fiction":
        response = f"""# 📚 Story Development Workshop

**Request:** {message.content}

## Character Development

Consider these elements for your main character:
- **Background**: What shaped them?
- **Motivation**: What drives them?
- **Conflict**: What stands in their way?
- **Change**: How will they transform?

## Plot Structure

1. **Setup**: Establish your world and characters
   - *Example:* "The old clock tower chimed midnight as Sarah realized she wasn't alone in the abandoned library."

2. **Conflict Introduction**: Present the central problem
   - *Example:* "The ancient book in her hands began to glow, revealing symbols that shouldn't exist."

3. **Rising Action**: Escalate the stakes
   - *Example:* "With each page she translated, another disappearance was reported in town."

4. **Climax**: The turning point
   - *Example:* "Standing at the center of the ritual circle, Sarah finally understood her role in the prophecy."

5. **Resolution**: How things settle
   - *Example:* "The morning sun rose on a changed world, one that would never know how close it came to darkness."

## Writing Prompts

If you need inspiration, try one of these starting points:
1. "The letter arrived exactly one year after the funeral."
2. "Nobody believed them about the sounds coming from the old well."
3. "The ability manifested on their thirteenth birthday."

Would you like me to develop any of these elements further or help with a specific aspect of your story?"""

    elif writing_type == "business":
        response = f"""# 📧 Professional Writing Assistant

**Request:** {message.content}

## Email Templates

### 1. Meeting Request

```
Subject: Request for Meeting: [Topic] - [Proposed Date]

Dear [Name],

I hope this email finds you well. I'm reaching out to request a meeting to discuss [specific topic/project].

Proposed details:
- Date: [date options]
- Time: [time options]
- Location/Platform: [in-person/Zoom/Teams]
- Agenda: [brief bullet points]

Please let me know if this works for your schedule or if you'd prefer an alternative time.

Thank you for your consideration.

Best regards,
[Your Name]
[Your Position]
[Contact Information]
```

### 2. Project Update

```
Subject: [Project Name] Update - [Date]

Dear [Recipient/Team],

I'm writing to provide an update on the current status of [Project Name].

Progress Highlights:
• [Key achievement 1]
• [Key achievement 2]
• [Key achievement 3]

Current Challenges:
• [Challenge 1] - [Mitigation plan]
• [Challenge 2] - [Mitigation plan]

Next Steps:
• [Upcoming task 1] - [Timeline]
• [Upcoming task 2] - [Timeline]

Please let me know if you have any questions or require additional information.

Regards,
[Your Name]
```

### 3. Follow-Up After Meeting

```
Subject: Follow-Up: [Meeting Topic] - [Meeting Date]

Hi [Name],

Thank you for your time during our meeting about [topic] on [date].

Key Takeaways:
• [Decision/point 1]
• [Decision/point 2]
• [Decision/point 3]

Action Items:
• [Your Name]: [Task] by [deadline]
• [Their Name]: [Task] by [deadline]
• [Team]: [Task] by [deadline]

I've attached [any relevant documents] for your reference.

Looking forward to our next steps.

Best,
[Your Name]
```

Would you like me to customize any of these templates for your specific situation?"""

    else:
        # General writing response
        response = f"""# ✍️ Creative Writing Assistant

**Request:** {message.content}

I'd be happy to help with your writing project! To provide the most tailored assistance, could you share more details about:

1. **Purpose**: What's the goal of this writing?
2. **Audience**: Who will be reading it?
3. **Tone**: Formal, casual, inspirational, technical?
4. **Length**: How long should it be?
5. **Key points**: What must be included?

## Writing Process Tips

While you consider those details, here are some general writing tips:

1. **Start with an outline** to organize your thoughts
2. **Write a rough draft** without worrying about perfection
3. **Edit for clarity** - simplify complex sentences
4. **Read aloud** to catch awkward phrasing
5. **Get feedback** from someone in your target audience

I'm ready to help with brainstorming, drafting, editing, or any other part of your writing process!"""

    await cl.Message(content=response).send()

async def handle_data_message(message):
    """Handle data analyst profile messages"""
    # Simplified implementation
    response = f"""# 📊 Data Analysis Plan

**Request:** {message.content}

## Analysis Approach

1. **Data Collection & Preparation**
   - Identify data sources
   - Clean and preprocess data
   - Handle missing values and outliers

2. **Exploratory Data Analysis**
   - Descriptive statistics
   - Distribution analysis
   - Correlation examination

3. **Visualization Strategy**
   - Time series plots for trends
   - Scatter plots for relationships
   - Bar charts for comparisons

4. **Statistical Analysis**
   - Hypothesis testing
   - Regression analysis
   - Significance evaluation

## Python Implementation Example

```python
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

# Load data
df = pd.read_csv('your_data.csv')

# Data cleaning
df = df.dropna()
df = df[(df['value'] > df['value'].quantile(0.01)) &
        (df['value'] < df['value'].quantile(0.99))]

# Exploratory analysis
print(df.describe())
correlation = df.corr()
sns.heatmap(correlation, annot=True)
plt.title('Correlation Matrix')
plt.show()

# Time series analysis
df['date'] = pd.to_datetime(df['date'])
df.set_index('date', inplace=True)
df.resample('M').mean().plot()
plt.title('Monthly Average Trend')
plt.show()

# Statistical testing
group1 = df[df['category'] == 'A']['value']
group2 = df[df['category'] == 'B']['value']
t_stat, p_value = stats.ttest_ind(group1, group2)
print(f"T-statistic: {t_stat}, P-value: {p_value}")
```

## Business Insights Framework

1. **Identify Key Metrics**
   - Revenue drivers
   - Customer behavior patterns
   - Operational efficiency indicators

2. **Comparative Analysis**
   - Historical performance
   - Industry benchmarks
   - Competitor positioning

3. **Actionable Recommendations**
   - Data-driven strategies
   - Prioritized by impact and effort
   - Implementation roadmap

Would you like me to focus on a specific part of this analysis plan or adapt it to your particular data challenge?"""

    await cl.Message(content=response).send()

async def handle_learning_message(message):
    """Handle learning tutor profile messages"""
    # Simplified implementation
    response = f"""# 🎓 Learning Module

**Topic:** {message.content}

## Concept Breakdown

Let's break this topic into manageable learning components:

### 1. Foundational Concepts
- Core principles and terminology
- Historical context and development
- Fundamental relationships

### 2. Practical Applications
- Real-world examples
- Case studies
- Problem-solving approaches

### 3. Advanced Topics
- Cutting-edge developments
- Specialized techniques
- Research frontiers

## Learning Path

**Week 1: Fundamentals**
- ✅ Read introductory materials
- ✅ Complete basic exercises
- ✅ Take concept quiz

**Week 2: Applied Practice**
- ✅ Work through example problems
- ✅ Build simple projects
- ✅ Participate in discussions

**Week 3: Mastery**
- ✅ Tackle complex challenges
- ✅ Create original work
- ✅ Teach concepts to others

## Learning Resources

**Recommended Reading:**
- Beginner: "Introduction to [Topic]" by [Author]
- Intermediate: "[Topic] in Practice" by [Author]
- Advanced: "[Topic] Mastery" by [Author]

**Online Courses:**
- [Platform]: [Course Name] - [Brief description]
- [Platform]: [Course Name] - [Brief description]

**Practice Tools:**
- [Tool/Platform] for interactive exercises
- [Tool/Platform] for project-based learning

Would you like me to elaborate on any specific part of this learning plan or focus on a particular concept within the topic?"""

    await cl.Message(content=response).send()

async def handle_productivity_message(message):
    """Handle productivity coach profile messages"""
    # Simplified implementation
    response = f"""# ⚡ Productivity System

**Request:** {message.content}

## Personalized Approach

Every effective productivity system should be tailored to your:
- **Working style** (focused vs. multitasking)
- **Energy patterns** (morning person vs. night owl)
- **Motivation drivers** (deadlines, rewards, accountability)
- **Environment** (home, office, co-working)

## Core Framework

### 1. Time Management
- **Time Blocking**: Schedule specific activities in dedicated blocks
- **Pomodoro Technique**: Work in focused 25-minute intervals
- **2-Minute Rule**: If a task takes less than 2 minutes, do it immediately

### 2. Task Management
- **Eisenhower Matrix**: Prioritize by urgency and importance
- **3-3-3 Method**: Identify 3 big tasks, 3 medium tasks, and 3 quick wins daily
- **Weekly Review**: Reflect on progress and adjust plans every week

### 3. Habit Building
- **Habit Stacking**: Link new habits to existing routines
- **Implementation Intentions**: "When X happens, I will do Y"
- **Minimum Viable Effort**: Start with tiny versions of habits

## Digital Tools Ecosystem

**Task Management:**
- Notion, Todoist, or ClickUp for comprehensive task tracking
- Google Calendar for time blocking

**Focus Tools:**
- Forest app for distraction blocking
- RescueTime for activity tracking

**Habit Tracking:**
- Habitica for gamified habit building
- Streaks for visual progress monitoring

## Implementation Plan

1. **Start Small**: Choose 1-2 techniques to implement this week
2. **Track Results**: Note energy levels and productivity
3. **Weekly Review**: Assess what's working and what isn't
4. **Iterate**: Adjust your system based on results
5. **Scale Up**: Gradually add more techniques as habits form

Would you like me to create a more specific plan based on your particular productivity challenges or goals?"""

    await cl.Message(content=response).send()

async def handle_default_message(message):
    """Handle messages for default profile"""
    response = f"""# 👋 Chat Profiles Demo

Thank you for your message: "{message.content}"

This demo showcases multiple specialized chat profiles:

**Available Profiles:**
- 🐍 **Coding Assistant** - Programming help and technical solutions
- ✍️ **Creative Writer** - Writing assistance and content creation
- 📊 **Data Analyst** - Data analysis and visualization
- 🎓 **Learning Tutor** - Educational content and explanations
- ⚡ **Productivity Coach** - Efficiency and organization tips

To experience specialized responses, please select a profile from the dropdown menu at the top of the chat interface.

Each profile provides:
- Customized welcome messages
- Specialized responses
- Relevant starter prompts
- Topic-specific formatting

*This message is from the default profile. Select a specific profile for enhanced assistance!*"""

    await cl.Message(content=response).send()

@cl.on_chat_end
def on_chat_end():
    """Handle chat session end and update analytics"""
    session_id = cl.context.session.id
    if session_id in user_sessions:
        session = user_sessions[session_id]
        profile_name = session.get("profile", "default")
        duration = datetime.now() - session.get("start_time", datetime.now())
        message_count = session.get("message_count", 0)

        print(f"👋 Chat session ended for profile {profile_name}. Duration: {duration}, Messages: {message_count}")
        del user_sessions[session_id]