import chainlit as cl

@cl.set_starters
async def set_starters():
    return [
        cl.<PERSON>er(
            label="Morning routine ideation",
            message="Can you help me create a personalized morning routine that would help increase my productivity throughout the day? Start by asking me about my current habits and what activities energize me in the morning.",
            icon="/public/idea.svg",
            ),

        cl.<PERSON><PERSON>(
            label="Explain superconductors",
            message="Explain superconductors like I'm five years old.",
            icon="/public/learn.svg",
            ),
        cl.<PERSON><PERSON>(
            label="Python script for daily email reports",
            message="Write a script to automate sending daily email reports in Python, and walk me through how I would set it up.",
            icon="/public/terminal.svg",
            ),
        cl.Starter(
            label="Text inviting friend to wedding",
            message="Write a text asking a friend to be my plus-one at a wedding next month. I want to keep it super short and casual, and offer an out.",
            icon="/public/write.svg",
            )
        ]

@cl.on_chat_start
async def on_chat_start():
    """Initialize chat session with welcome message"""
    print("🚀 New chat session started with starters!")

    await cl.Message(
        content="# 🎉 Welcome to Chainlit with Conversation Starters!\n\nChoose one of the conversation starters above to begin, or type your own message!"
    ).send()

@cl.on_message
async def main(message: cl.Message):
    """Handle incoming messages"""
    print(f"📨 User sent: {message.content}")

    # Enhanced response based on the starter topic
    content = message.content.lower()

    if "morning routine" in content:
        response = f"""# 🌅 Morning Routine Planning

**Your Request:** {message.content}

Great choice! Let me help you create a personalized morning routine. To get started, I'd like to know:

1. **Current Wake-up Time:** What time do you usually wake up?
2. **Energy Levels:** Are you naturally a morning person or night owl?
3. **Current Habits:** What does your current morning look like?
4. **Goals:** What do you want to achieve with a better routine?
5. **Time Available:** How much time can you dedicate to your morning routine?

Once I know more about your situation, I can suggest a customized routine that fits your lifestyle and goals! ✨"""

    elif "superconductor" in content:
        response = f"""# 🔬 Superconductors Explained Simply

**Your Question:** {message.content}

Great question! Let me explain superconductors like you're five:

🧊 **What are superconductors?**
Imagine electricity flowing through wires like water through a garden hose. Normally, the "hose" (wire) slows down the water (electricity) a little bit.

⚡ **The Magic:**
Superconductors are special materials that, when they get REALLY cold, let electricity flow through them with ZERO resistance - like a perfect, frictionless slide!

🌡️ **The Cold Part:**
Most superconductors only work when they're colder than a freezer - we're talking about temperatures colder than outer space!

🚀 **Cool Uses:**
- Super-fast trains that float on magnets (maglev trains)
- MRI machines in hospitals
- Powerful electromagnets for research

Think of it like a magical highway where cars (electricity) can drive at full speed with no traffic jams! 🏎️"""

    elif "python" in content and "email" in content:
        response = f"""# 🐍 Python Email Automation Script

**Your Request:** {message.content}

Excellent! Let me help you create an automated email reporting script. Here's a complete solution:

```python
import smtplib
import schedule
import time
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime

def send_daily_report():
    # Email configuration
    sender_email = "<EMAIL>"
    sender_password = "your_app_password"  # Use app password for Gmail
    recipient_email = "<EMAIL>"

    # Create message
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = recipient_email
    message["Subject"] = f"Daily Report - {{datetime.now().strftime('%Y-%m-%d')}}"

    # Email body
    body = f'''
    Daily Report for {{datetime.now().strftime('%B %d, %Y')}}

    📊 Key Metrics:
    - Tasks completed: [Your data here]
    - Revenue: [Your data here]
    - Active users: [Your data here]

    Generated automatically at {{datetime.now().strftime('%H:%M')}}
    '''

    message.attach(MIMEText(body, "plain"))

    # Send email
    try:
        server = smtplib.SMTP("smtp.gmail.com", 587)
        server.starttls()
        server.login(sender_email, sender_password)
        server.sendmail(sender_email, recipient_email, message.as_string())
        server.quit()
        print("✅ Daily report sent successfully!")
    except Exception as e:
        print(f"❌ Error: {{e}}")

# Schedule the email
schedule.every().day.at("09:00").do(send_daily_report)

# Keep the script running
while True:
    schedule.run_pending()
    time.sleep(60)
```

**Setup Steps:**
1. Install required packages: `pip install schedule`
2. Enable 2FA on Gmail and create an app password
3. Replace email credentials and customize the report content
4. Run the script - it will send daily reports at 9 AM

Would you like me to explain any part in more detail? 🚀"""

    elif "wedding" in content or "plus-one" in content:
        response = f"""# 💌 Wedding Plus-One Text

**Your Request:** {message.content}

Perfect! Here are a few casual, short options for asking your friend:

**Option 1 (Super Casual):**
"Hey! I have a wedding next month and need a plus-one. Want to come eat free food and judge people's dance moves with me? 😄 No pressure if you're busy!"

**Option 2 (Slightly More Formal):**
"Hi! I'm going to a wedding next month and would love to have you as my plus-one if you're free. It should be fun! Let me know if you're interested - no worries if not! 💕"

**Option 3 (With Details):**
"Hey! Wedding invitation came with a plus-one and I immediately thought of you 😊 It's [date] at [location]. Want to join me? Totally fine if you can't make it!"

**Key Elements:**
✅ Casual and friendly tone
✅ Offers an easy out
✅ Mentions it's a plus-one (not a date)
✅ Shows you thought of them specifically

Pick the one that matches your friendship style! 🎉"""

    else:
        # Default response for other messages
        response = f"""# 💬 Message Received!

**Your Message:** {message.content}

Thanks for your message! I can help you with:

🌅 **Morning Routines** - Productivity and habit building
🔬 **Educational Topics** - Explaining complex concepts simply
🐍 **Python Coding** - Scripts, automation, and programming help
💌 **Creative Writing** - Texts, emails, and communication

Feel free to ask about any of these topics, or try one of the conversation starters above! ✨

*Word count: {len(message.content.split())} words*"""

    await cl.Message(content=response).send()

@cl.on_stop
def on_stop():
    """Handle task stopping"""
    print("⏹️ User stopped the current task!")

@cl.on_chat_end
def on_chat_end():
    """Handle chat session end"""
    print("👋 Chat session ended!")