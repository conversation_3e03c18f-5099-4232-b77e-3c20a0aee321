import chainlit as cl
import asyncio
from openai import AsyncOpenAI
from config import AIConfig

# Demo tokens for initial streaming
token_list = ["the", "quick", "brown", "fox", "jumps", "over", "the", "lazy", "dog"]

# Initialize AI configuration (defaults to Gemini, falls back to OpenAI)
try:
    config = AIConfig("gemini")  # Try Gemini first
    print(f"✅ Using {config.provider.upper()} with model: {config.get_model()}")
except ValueError:
    try:
        config = AIConfig("openai")  # Fallback to OpenAI
        print(f"✅ Using {config.provider.upper()} with model: {config.get_model()}")
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        print("💡 Please check your .env file and API keys")
        # Create a dummy config for demo mode
        config = None

# Initialize client and model if config is available
if config:
    client = config.get_client()
    model = config.get_model()
else:
    client = None
    model = "demo-mode"
    print("🔧 Running in demo mode - AI streaming disabled")

@cl.on_chat_start
async def on_chat_start():
    """Initialize chat session with welcome message"""
    print("🚀 New chat session started!")

    # Create a message for streaming
    msg = cl.Message(content="")

    # Stream tokens one by one
    for token in token_list:
        await msg.stream_token(token + " ")

    # Send the final message
    await msg.send()

@cl.on_message
async def main(message: cl.Message):
    """Handle incoming messages with real AI streaming response"""
    print(f"📨 User sent: {message.content}")

    # Create message history for AI
    message_history = [
        {"role": "system", "content": "You are a helpful AI assistant. Provide clear, concise responses."},
        {"role": "user", "content": message.content}
    ]

    # Use the AI streaming function if available
    if client and config:
        try:
            await ai_stream_response(message_history)
        except Exception as e:
            print(f"❌ AI streaming error: {e}")
            # Fallback to demo streaming
            await demo_streaming_response(message.content)
    else:
        # Demo mode
        await demo_streaming_response(message.content)

async def demo_streaming_response(user_message: str):
    """Fallback demo streaming when AI is unavailable"""
    msg = cl.Message(content="")

    response_tokens = [
        "I", "understand", "you", "said:", f'"{user_message}".',
        "This", "is", "a", "demo", "streaming", "response", "since",
        "AI", "streaming", "is", "currently", "unavailable."
    ]

    for token in response_tokens:
        await msg.stream_token(token + " ")
        await asyncio.sleep(0.1)

    await msg.send()

@cl.step(type="llm")
async def ai_stream_response(message_history):
    """Stream AI response using configured model"""
    # Get settings from config
    settings = config.get_settings()
    settings.update({
        "temperature": 0.7,  # Override for more creative responses
        "max_tokens": 500,   # Limit response length
    })

    try:
        # Create streaming completion
        stream = await client.chat.completions.create(
            messages=message_history,
            stream=True,
            **settings
        )

        current_step = cl.context.current_step

        # Stream the response
        async for part in stream:
            if hasattr(part, 'choices') and part.choices:
                delta = part.choices[0].delta
                if hasattr(delta, 'content') and delta.content:
                    await current_step.stream_token(delta.content)

    except Exception as e:
        print(f"❌ Streaming error: {e}")
        # Send error message
        error_msg = cl.Message(content=f"⚠️ AI streaming error: {str(e)}")
        await error_msg.send()
        raise

# Alternative function name for backward compatibility
async def gpt4(message_history):
    """Backward compatible function name"""
    return await ai_stream_response(message_history)