import chainlit as cl
from openai import AsyncOpenAI
from config import Config

token_list = ["the", "quick", "brown", "fox", "jumps", "over", "the", "lazy", "dog"]

@cl.on_chat_start
async def on_chat_start():
    """Initialize chat session with welcome message"""
    print("🚀 New chat session started!")

    # Create a message for streaming
    msg = cl.Message(content="")

    # Stream tokens one by one
    for token in token_list:
        await msg.stream_token(token + " ")

    # Send the final message
    await msg.send()

@cl.on_message
async def main(message: cl.Message):
    """Handle incoming messages with streaming response"""
    print(f"📨 User sent: {message.content}")

    # Create a streaming response
    msg = cl.Message(content="")

    # Simulate AI response streaming
    response_tokens = [
        "I", "understand", "you", "said:", f'"{message.content}".',
        "Let", "me", "provide", "a", "streaming", "response", "to",
        "demonstrate", "real-time", "text", "generation!"
    ]

    for token in response_tokens:
        await msg.stream_token(token + " ")
        # Add a small delay to make streaming visible
        import asyncio
        await asyncio.sleep(0.1)

    await msg.send()

@cl.step(type="llm")
async def gpt4(message_history): # message_history is a list of messages
    settings = {
        "model": "gpt-4",
        "temperature": 0,
    }

    stream = await client.chat.completions.create(
        messages=message_history, stream=True, **settings
    )

    current_step = cl.context.current_step

    async for part in stream:
        delta = part.choices[0].delta

        if delta.content:
            # Stream the output of the step
            await current_step.stream_token(delta.content)