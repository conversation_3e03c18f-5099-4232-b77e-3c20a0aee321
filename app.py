import chainlit as cl
from chainlit.types import ThreadDict
import asyncio
import time
from datetime import datetime

# Store user sessions
user_sessions = {}

@cl.on_chat_start
async def on_chat_start():
    """Initialize chat session with welcome message and user setup"""
    print(f"🚀 New chat session started at {datetime.now()}")

    # Store session info
    user_sessions[cl.context.session.id] = {
        "start_time": datetime.now(),
        "message_count": 0,
        "user_name": None
    }

    # Create action buttons
    actions = [
        cl.Action(name="get_help", value="help", description="Get help"),
        cl.Action(name="clear_chat", value="clear", description="Clear chat"),
        cl.Action(name="show_stats", value="stats", description="Show session stats")
    ]

    # Send welcome message with actions
    await cl.Message(
        content="""# 🎉 Welcome to Enhanced Chainlit Chat!

I'm your AI assistant. Here's what I can do:

✨ **Features:**
- Echo your messages with enhancements
- Handle file uploads
- Process action buttons
- Track session statistics
- Support async operations

💡 **Try these commands:**
- Type any message to get an enhanced response
- Upload a file to see file handling
- Use the action buttons below
- Type "help" for more commands

What would you like to do first?""",
        actions=actions
    ).send()

@cl.on_message
async def main(message: cl.Message):
    """Enhanced message handler with multiple features"""
    session_id = cl.context.session.id
    session = user_sessions.get(session_id, {})

    # Update message count
    session["message_count"] = session.get("message_count", 0) + 1
    user_sessions[session_id] = session

    print(f"📨 User sent: {message.content}")

    # Handle special commands
    content = message.content.lower().strip()

    if content == "help":
        await send_help_message()
        return

    if content == "stats":
        await send_stats_message(session)
        return

    if content.startswith("delay"):
        await handle_delay_command(content)
        return

    # Handle file attachments
    if message.elements:
        await handle_file_upload(message)
        return

    # Default enhanced response
    await send_enhanced_response(message, session)

async def send_help_message():
    """Send comprehensive help message"""
    help_content = """# 🆘 Help & Commands

## Available Commands:
- **help** - Show this help message
- **stats** - Show session statistics
- **delay [seconds]** - Simulate processing delay
- **clear** - Clear the chat (use action button)

## Features:
- 📁 **File Upload**: Drag & drop files to analyze them
- 🔘 **Action Buttons**: Use buttons for quick actions
- 📊 **Session Tracking**: Your activity is tracked
- ⚡ **Async Processing**: Handles long-running tasks

## Tips:
- Try uploading different file types
- Use action buttons for quick commands
- Messages are enhanced with metadata
- Session persists during your visit

Need more help? Just ask! 😊"""

    await cl.Message(content=help_content).send()

async def send_stats_message(session):
    """Send session statistics"""
    start_time = session.get("start_time", datetime.now())
    duration = datetime.now() - start_time
    message_count = session.get("message_count", 0)

    stats_content = f"""# 📊 Session Statistics

**Session Info:**
- 🕐 Started: {start_time.strftime('%H:%M:%S')}
- ⏱️ Duration: {str(duration).split('.')[0]}
- 💬 Messages: {message_count}
- 🆔 Session ID: {cl.context.session.id[:8]}...

**Performance:**
- ⚡ Average response time: < 1 second
- 🔄 Status: Active and responsive

Keep chatting to see these numbers grow! 📈"""

    await cl.Message(content=stats_content).send()

async def handle_delay_command(content):
    """Handle delay command for demonstration"""
    try:
        # Extract delay time
        parts = content.split()
        delay_time = int(parts[1]) if len(parts) > 1 else 3
        delay_time = min(delay_time, 10)  # Max 10 seconds

        # Send processing message
        msg = cl.Message(content=f"⏳ Processing for {delay_time} seconds...")
        await msg.send()

        # Simulate processing
        await asyncio.sleep(delay_time)

        # Update message
        msg.content = f"✅ Processing completed after {delay_time} seconds!"
        await msg.update()

    except (ValueError, IndexError):
        await cl.Message(
            content="❌ Invalid delay command. Use: `delay [seconds]` (max 10)"
        ).send()

async def handle_file_upload(message):
    """Handle file uploads with analysis"""
    files_info = []

    for element in message.elements:
        if hasattr(element, 'name') and hasattr(element, 'size'):
            files_info.append(f"- **{element.name}** ({element.size} bytes)")

    response_content = f"""# 📁 File Upload Detected!

**Uploaded Files:**
{chr(10).join(files_info)}

**Analysis:**
- File type detection: ✅
- Size validation: ✅
- Content preview: Available
- Processing status: Ready

**Note:** This is a demo. In a real app, you could:
- Parse file contents
- Extract text/data
- Generate summaries
- Store files securely

Would you like me to process these files further?"""

    await cl.Message(content=response_content).send()

async def send_enhanced_response(message, session):
    """Send enhanced response with metadata"""
    word_count = len(message.content.split())
    char_count = len(message.content)
    message_num = session.get("message_count", 0)

    response_content = f"""# 💬 Message Received & Enhanced!

**Your Message:** "{message.content}"

**Analysis:**
- 📝 Words: {word_count}
- 🔤 Characters: {char_count}
- 📊 Message #{message_num} in this session
- 🕐 Timestamp: {datetime.now().strftime('%H:%M:%S')}

**Enhanced Response:**
I received your message and processed it successfully! This enhanced version includes metadata and formatting to make our conversation more interactive and informative.

*Try uploading a file or using the action buttons for more features!* ✨"""

    await cl.Message(content=response_content).send()

@cl.on_action
async def on_action(action):
    """Handle action button clicks"""
    print(f"🔘 Action triggered: {action.name} = {action.value}")

    if action.value == "help":
        await send_help_message()
    elif action.value == "clear":
        await cl.Message(content="🧹 Chat cleared! Start fresh with a new message.").send()
    elif action.value == "stats":
        session = user_sessions.get(cl.context.session.id, {})
        await send_stats_message(session)

@cl.on_stop
def on_stop():
    """Handle task stopping"""
    print("⏹️ User stopped the current task!")

@cl.on_chat_end
def on_chat_end():
    """Handle chat session end"""
    session_id = cl.context.session.id
    if session_id in user_sessions:
        session = user_sessions[session_id]
        duration = datetime.now() - session.get("start_time", datetime.now())
        print(f"👋 Chat session ended. Duration: {duration}, Messages: {session.get('message_count', 0)}")
        del user_sessions[session_id]

@cl.on_chat_resume
async def on_chat_resume(thread: ThreadDict):
    """Handle chat session resumption"""
    print(f"🔄 User resumed chat session: {thread.get('id', 'Unknown')}")
    await cl.Message(
        content="👋 Welcome back! Your previous conversation has been restored."
    ).send()