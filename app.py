import chainlit as cl

@cl.on_chat_start
async def on_chat_start():
    """Initialize chat session with welcome message"""
    print("🚀 New chat session started!")

    await cl.Message(
        content="# 🎉 Welcome to Enhanced Chainlit Chat!\n\nI'm your AI assistant. Send me a message to get started!"
    ).send()

@cl.on_message
async def main(message: cl.Message):
    """Simple message handler"""
    print(f"📨 User sent: {message.content}")

    # Send enhanced response
    await cl.Message(
        content=f"**Received:** {message.content}\n\n✨ This is your enhanced response! The message had {len(message.content.split())} words."
    ).send()

@cl.on_stop
def on_stop():
    """Handle task stopping"""
    print("⏹️ User stopped the current task!")

@cl.on_chat_end
def on_chat_end():
    """Handle chat session end"""
    print("👋 Chat session ended!")