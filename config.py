# config.py - Enhanced version for OpenAI/Gemini compatibility
import os
from dotenv import load_dotenv, find_dotenv
from openai import AsyncOpenAI
from typing import Optional

class AIConfig:
    """Configuration class supporting both OpenAI and Gemini APIs"""

    def __init__(self, provider: str = "gemini"):
        """
        Initialize configuration
        Args:
            provider: "openai" or "gemini" (default: "gemini")
        """
        load_dotenv(find_dotenv())
        self.provider = provider.lower()

        if self.provider == "gemini":
            self.api_key = os.getenv("GEMINI_API_KEY")
            self.model_name = os.getenv("MODEL_NAME", "gemini-2.0-flash-exp")
            self.base_url = "https://generativelanguage.googleapis.com/v1beta/openai/"
        else:  # openai
            self.api_key = os.getenv("OPENAI_API_KEY")
            self.model_name = os.getenv("MODEL_NAME", "gpt-4")
            self.base_url = "https://api.openai.com/v1"

        self.temperature = float(os.getenv("TEMPERATURE", "0.7"))
        self.max_tokens = int(os.getenv("MAX_TOKENS", "1000"))

        # Validate API key
        if not self.api_key:
            key_name = "GEMINI_API_KEY" if self.provider == "gemini" else "OPENAI_API_KEY"
            raise ValueError(f"{key_name} not found. Please set environment variable.")

    def get_client(self) -> AsyncOpenAI:
        """Get configured AsyncOpenAI client"""
        return AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )

    def get_model(self) -> str:
        """Get configured model name (compatible with stream.py)"""
        return self.model_name

    def get_settings(self) -> dict:
        """Get model settings dictionary"""
        return {
            "model": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "stream": True
        }

    def get_provider_info(self) -> dict:
        """Get provider information"""
        return {
            "provider": self.provider,
            "model": self.model_name,
            "base_url": self.base_url,
            "api_key_set": bool(self.api_key)
        }

    def __str__(self) -> str:
        """String representation"""
        return f"AIConfig(provider={self.provider}, model={self.model_name})"

# Helper function to create environment file
def create_env_template():
    """Create a .env template file"""
    env_content = """# AI Configuration
# Choose your provider and set the appropriate API key

# For Gemini (Google AI)
GEMINI_API_KEY=your_gemini_api_key_here
MODEL_NAME=gemini-2.0-flash-exp

# For OpenAI (uncomment if using OpenAI)
# OPENAI_API_KEY=your_openai_api_key_here
# MODEL_NAME=gpt-4

# Optional settings
TEMPERATURE=0.7
MAX_TOKENS=1000
"""

    if not os.path.exists(".env"):
        with open(".env", "w") as f:
            f.write(env_content)
        print("✅ Created .env template file")
        print("📝 Please edit .env and add your API key")
    else:
        print("⚠️  .env file already exists")

# Setup helper
def setup_config():
    """Interactive setup helper"""
    print("🔧 AI Configuration Setup")
    print("=" * 40)

    # Check for .env file
    if not os.path.exists(".env"):
        print("📝 No .env file found. Creating template...")
        create_env_template()
        return None

    # Try both providers
    providers_status = {}

    for provider in ["gemini", "openai"]:
        try:
            config = AIConfig(provider=provider)
            providers_status[provider] = {
                "available": True,
                "model": config.get_model(),
                "info": config.get_provider_info()
            }
            print(f"✅ {provider.upper()} configuration valid")
        except ValueError as e:
            providers_status[provider] = {
                "available": False,
                "error": str(e)
            }
            print(f"❌ {provider.upper()}: {e}")

    # Return available provider
    if providers_status["gemini"]["available"]:
        return AIConfig("gemini")
    elif providers_status["openai"]["available"]:
        return AIConfig("openai")
    else:
        print("\n⚠️  No valid API configuration found")
        return None

if __name__ == "__main__":
    config = setup_config()
    if config:
        print(f"\n🚀 Ready to use {config.provider.upper()}!")
        print("Run: chainlit run stream.py -w")
    else:
        print("\n📝 Please configure your API keys in .env file")