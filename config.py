# config.py - Enhanced version
import os
from dotenv import load_dotenv, find_dotenv

class AIConfig:
    def __init__(self):
        load_dotenv(find_dotenv())
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        self.model_name = os.getenv("MODEL_NAME", "gemini-2.0-flash")
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/openai/"
    
    def get_client(self):
        return AsyncOpenAI(
            api_key=self.gemini_api_key,
            base_url=self.base_url
        )
    
    def get_model(self):
        return OpenAIChatCompletionsModel(
            model=self.model_name,
            openai_client=self.get_client()
        )